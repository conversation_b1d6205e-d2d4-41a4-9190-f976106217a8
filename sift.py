import cv2
from matplotlib import pyplot as plt
import numpy as np
from collections import Counter
match_counter = Counter()
def find_fundamental_matrix(img1_path, img2_path):
    # Đọc ảnh
    img1 = cv2.imread(img1_path, cv2.IMREAD_GRAYSCALE)
    img2 = cv2.imread(img2_path, cv2.IMREAD_GRAYSCALE)

    # Khởi tạo SIFT detector
    sift = cv2.SIFT_create()

    # Tìm keypoints và descriptors
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    # Dùng FLANN matcher để ghép các điểm đặc trưng
    FLANN_INDEX_KDTREE = 1
    index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
    search_params = dict(checks=50)
    flann = cv2.FlannBasedMatcher(index_params, search_params)
    matches = flann.knnMatch(des1, des2, k=2)

    # Lọc các match tốt theo <PERSON>'s ratio test
    good = []
    pts1 = []
    pts2 = []
    for m, n in matches:
        if m.distance < 0.55 * n.distance:
            good.append(m)
            pts1.append(kp1[m.queryIdx].pt)
            pts2.append(kp2[m.trainIdx].pt)

    pts1 = np.int32(pts1)
    pts2 = np.int32(pts2)

    return pts1, pts2
def draw_keypoints_side_by_side(img1_path, pts1, img2_path, pts2, match_counter=None, top_n=10):
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    # Nếu có match_counter, chỉ vẽ các match xuất hiện nhiều nhất
    if match_counter:
        # Lấy top_n match xuất hiện nhiều nhất
        top_matches = set([match for match, _ in match_counter.most_common(top_n)])
        for pt1, pt2 in zip(pts1, pts2):
            key = (int(pt1[0]), int(pt1[1]), int(pt2[0]), int(pt2[1]))
            if key in top_matches:
                cv2.circle(img1, (int(pt1[0]), int(pt1[1])), 8, (255, 0, 0), 3)
                cv2.circle(img2, (int(pt2[0]), int(pt2[1])), 8, (255, 0, 0), 3)
    else:
        for pt in pts1:
            x, y = pt
            cv2.circle(img1, (int(x), int(y)), 4, (0, 255, 0), 2)
        for pt in pts2:
            x, y = pt
            cv2.circle(img2, (int(x), int(y)), 4, (0, 255, 0), 2)
    img1_rgb = cv2.cvtColor(img1, cv2.COLOR_BGR2RGB)
    img2_rgb = cv2.cvtColor(img2, cv2.COLOR_BGR2RGB)
    plt.figure(figsize=(18, 8))
    plt.subplot(1, 2, 1)
    plt.title("Keypoints Image 1")
    plt.imshow(img1_rgb)
    plt.axis('off')
    plt.subplot(1, 2, 2)
    plt.title("Keypoints Image 2")
    plt.imshow(img2_rgb)
    plt.axis('off')
    plt.show()
def draw_epipolar_line(img_path, F, pt, img_shape=None):
    """
    Vẽ epipolar line trên ảnh img_path với điểm pt (tọa độ trên ảnh còn lại) và ma trận F.
    pt: (x, y) trên ảnh 1, vẽ epipolar line trên ảnh 2.
    """
    img = cv2.imread(img_path)
    if img_shape is not None:
        h, w = img_shape
    else:
        h, w = img.shape[:2]
    # Tạo điểm đồng nhất
    x, y = pt
    point = np.array([x, y, 1]).reshape(3, 1)
    # Tính epipolar line l' = F * x
    line = F @ point  # (3,1)
    a, b, c = line.flatten()
    # Tìm 2 điểm cắt biên ảnh để vẽ đường thẳng
    pts = []
    for x0 in [0, w-1]:
        # y = -(a*x + c)/b
        if abs(b) > 1e-6:
            y0 = int(round(-(a * x0 + c) / b))
            if 0 <= y0 < h:
                pts.append((x0, y0))
    for y0 in [0, h-1]:
        # x = -(b*y + c)/a
        if abs(a) > 1e-6:
            x0 = int(round(-(b * y0 + c) / a))
            if 0 <= x0 < w:
                pts.append((x0, y0))
    # Lấy 2 điểm khác nhau để vẽ
    if len(pts) >= 2:
        cv2.line(img, pts[0], pts[1], (0, 0, 255), 2)
    # Hiển thị ảnh
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    plt.figure(figsize=(10, 10))
    plt.imshow(img_rgb)
    plt.title("Epipolar Line")
    plt.axis('off')
    plt.show()
def point_line_distance(pt, line):
    """
    Tính khoảng cách từ điểm pt (x, y) đến đường thẳng ax + by + c = 0
    line: (a, b, c)
    """
    x0, y0 = pt
    a, b, c = line
    return abs(a * x0 + b * y0 + c) / np.sqrt(a**2 + b**2)


if __name__ == "__main__":
    srcip = "D:\\AI\\CameraCalib\\human_frame_ip\\"
    srcand = "D:\\AI\\CameraCalib\\human_frame_and\\"
    image_pairs = []
    for i in range(938, 970):
        image_pairs.append((srcand + f"frame_00{i:04d}.jpg", srcip + f"frame_00{i+189:04d}.jpg"))

    # Đếm tần suất match trên toàn bộ cặp ảnh
    for img1_path, img2_path in image_pairs:
        pts1, pts2 = find_fundamental_matrix(img1_path, img2_path)
        for pt1, pt2 in zip(pts1, pts2):
            key = (int(pt1[0]), int(pt1[1]), int(pt2[0]), int(pt2[1]))
            match_counter[key] += 1

    # Lấy top N match xuất hiện nhiều nhất để tính F
    top_n = 15  # Số lượng match nhiều nhất muốn lấy, bạn có thể điều chỉnh
    top_matches = match_counter.most_common(top_n)
    if top_matches:
        pts1_top = []
        pts2_top = []
        for match, count in top_matches:
            pts1_top.append([match[0], match[1]])
            pts2_top.append([match[2], match[3]])
        pts1_top = np.int32(pts1_top)
        pts2_top = np.int32(pts2_top)

        # Tính F dựa trên các cặp điểm có tần suất xuất hiện nhiều nhất
        F, mask = cv2.findFundamentalMat(pts1_top, pts2_top, cv2.FM_RANSAC)
        print("Fundamental Matrix (từ các match xuất hiện nhiều nhất):\n", F)

        # Lấy match xuất hiện nhiều nhất để kiểm tra và vẽ epipolar line
        match, count = top_matches[1]
        print(f"Match {match} xuất hiện {count} lần")
        pt1 = (match[0], match[1])  # điểm trên ảnh 1
        pt2 = (match[2], match[3])  # điểm trên ảnh 2

        # Vẽ epipolar line với pt1 trên ảnh 2
        draw_epipolar_line(image_pairs[0][1], F, pt1)

        # Tính epipolar line l' = F * [pt1[0], pt1[1], 1]^T
        src_pt = np.array([pt1[0], pt1[1], 1]).reshape(3, 1)
        epi_line = F @ src_pt
        epi_line = epi_line.flatten()

        # Kiểm tra pt2 có nằm trên epipolar line không
        dist = point_line_distance(pt2, epi_line)
        print(f"Khoảng cách từ điểm {pt2} đến epipolar line là: {dist:.2f} pixels")
        if dist < 2:
            print(f"Điểm {pt2} nằm trên epipolar line.")
        else:
            print(f"Điểm {pt2} KHÔNG nằm trên epipolar line.")