import cv2
import numpy as np
import matplotlib.pyplot as plt
from ultralytics import YOLO

# Fundamental Matrix đã cho
F = np.array([
    [ 2.11509357e-05, -1.63916686e-04,  1.08249212e-01],
    [ 2.18518610e-04,  5.82194909e-05, -1.40515212e-01],
    [-1.22674236e-01,  7.56900246e-02,  1.00000000e+00]
])

def get_epipolar_line_points(line, img_shape, num_samples=50):
    h, w = img_shape[:2]
    a, b, c = line.flatten()

    # Lấy num_samples điểm trên đường epipolar
    x_vals = np.linspace(0, w-1, num_samples)
    points = []
    for x0 in x_vals:
        if abs(b) > 1e-6:
            y0 = -(a * x0 + c) / b
            if 0 <= y0 < h:
                points.append((int(round(x0)), int(round(y0))))
    return points

def find_best_sift_match(imgA, imgB, ptA, line):
    sift = cv2.SIFT_create()

    # 1. Tạo keypoint tại ptA
    kpA = [cv2.KeyPoint(float(ptA[0]), float(ptA[1]), 11)]
    _, desA = sift.compute(imgA, kpA)

    # 2. Lấy danh sách điểm trên epipolar line ảnh B
    line_pts = get_epipolar_line_points(line, imgB.shape)

    # 3. Tạo keypoints tại mỗi điểm trên epipolar line ảnh B
    kpB = [cv2.KeyPoint(float(x), float(y), 11) for (x, y) in line_pts]
    _, desB = sift.compute(imgB, kpB)

    if desB is None or desA is None:
        return None

    # 4. So sánh SIFT descriptor bằng khoảng cách Euclidean
    best_idx = np.argmin(np.linalg.norm(desB - desA[0], axis=1))
    return line_pts[best_idx]  # Tọa độ điểm tốt nhất trong ảnh B

def draw_epipolar_line(img_pathA, img_pathB, F, pt):
    imgB = cv2.imread(img_pathB)
    imgA = cv2.imread(img_pathA)
    h, w = imgB.shape[:2]
    x, y = pt
    point = np.array([x, y, 1]).reshape(3, 1)
    line = F @ point
    a, b, c = line.flatten()
    pts = []
    for x0 in [0, w-1]:
        if abs(b) > 1e-6:
            y0 = int(round(-(a * x0 + c) / b))
            if 0 <= y0 < h:
                pts.append((x0, y0))
    for y0 in [0, h-1]:
        if abs(a) > 1e-6:
            x0 = int(round(-(b * y0 + c) / a))
            if 0 <= x0 < w:
                pts.append((x0, y0))
    if len(pts) >= 2:
        cv2.line(imgB, pts[0], pts[1], (0, 0, 255), 2)
    points = find_best_sift_match(imgA, imgB, pt, line)
    img_rgb = cv2.cvtColor(imgB, cv2.COLOR_BGR2RGB)
    plt.figure(figsize=(10, 10))
    plt.imshow(img_rgb)
    plt.title("Epipolar Line")
    plt.axis('off')
    plt.show()
    return line, points



if __name__ == "__main__":
    img_path_1 = "D:\\AI\\CameraCalib\\human_frame_and\\frame_000938.jpg" 
    img_path_2 = "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001127.jpg" 
    # Detect pose bằng YOLOv8n-pose
    model = YOLO("yolov8n-pose.pt")
    results1 = model(img_path_1)
    results2 = model(img_path_2)
    keypoints = results1[0].keypoints.xy.cpu().numpy()  # (num_person, num_kpts, 2)
    keypoints2 = results2[0].keypoints.xy.cpu().numpy()  # (num_person, num_kpts, 2)
    first_pt = tuple(keypoints[0][1])  # Lấy keypoint đầu tiên của người đầu tiên
    first_pt2 = tuple(keypoints2[0][1])
    print(f"Tọa độ đầu tiên detect được: {first_pt}")

    # Vẽ epipolar line
    epipolar_line, points = draw_epipolar_line( img_path_1,img_path_2, F, first_pt)
    print(f"Tọa độ của điểm tốt nhất trên epipolar line: {points}") 

